"""
意图理解器 - 专门负责分析用户意图和提取关键信息
"""

import re
import logging
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class IntentType(Enum):
    """意图类型枚举"""
    SIMPLE_QUERY = "simple_query"          # 简单查询
    COMPLEX_ANALYSIS = "complex_analysis"   # 复杂分析（需要多步骤）
    KNOWLEDGE_SEARCH = "knowledge_search"   # 知识搜索
    MIXED_TASK = "mixed_task"              # 混合任务


@dataclass
class IntentResult:
    """意图分析结果"""
    intent_type: IntentType
    confidence: float
    user_question: str
    extracted_params: Dict[str, Any]
    requires_multi_step: bool
    step_requirements: List[str]
    province_code: Optional[str] = None
    time_range: Optional[str] = None
    target_database: Optional[str] = None
    clarification_needed: bool = False
    clarification_reason: str = ""


class IntentAnalyzer:
    """
    意图理解器
    
    职责：
    1. 分析用户输入的意图类型
    2. 提取关键参数（省份、时间、指标等）
    3. 判断是否需要多步骤处理
    4. 识别是否需要澄清
    """
    
    def __init__(self):
        self.complex_keywords = [
            "风险", "分析", "明细", "详情", "对比", "趋势", 
            "存在哪些", "有什么", "如何", "为什么"
        ]
        self.simple_keywords = [
            "是多少", "有多少", "查询", "显示", "列出"
        ]
        
    def analyze_intent(self, user_input: str, system_message: str = None, 
                      vector_prompts: List = None) -> IntentResult:
        """
        分析用户意图
        
        Args:
            user_input: 用户输入
            system_message: 系统消息
            vector_prompts: 向量匹配的复杂提示词
            
        Returns:
            IntentResult: 意图分析结果
        """
        logger.info(f"🔍 [意图分析] 开始分析用户输入: {user_input}")
        
        # 提取纯粹的用户问题
        user_question = self._extract_user_question(user_input)
        logger.info(f"🎯 [意图分析] 提取的用户问题: {user_question}")
        
        # 提取省份代码
        province_code = self._extract_province_code(system_message or user_input)
        
        # 提取时间范围
        time_range = self._extract_time_range(user_question)
        
        # 判断意图类型
        intent_type, confidence = self._classify_intent(user_question, vector_prompts)
        
        # 判断是否需要多步骤处理
        requires_multi_step, step_requirements = self._analyze_step_requirements(
            user_question, vector_prompts
        )
        
        # 构建结果
        result = IntentResult(
            intent_type=intent_type,
            confidence=confidence,
            user_question=user_question,
            extracted_params={
                "province_code": province_code,
                "time_range": time_range,
                "original_input": user_input
            },
            requires_multi_step=requires_multi_step,
            step_requirements=step_requirements,
            province_code=province_code,
            time_range=time_range,
            target_database=self._map_province_to_database(province_code)
        )
        
        logger.info(f"✅ [意图分析] 分析完成: {intent_type.value}, 置信度: {confidence:.3f}, 多步骤: {requires_multi_step}")
        
        return result
    
    def _extract_user_question(self, user_input: str) -> str:
        """从用户输入中提取纯粹的问题"""
        # 移除数据库指定部分
        question = re.sub(r'数据库\s*[:：]\s*[A-Z]+\s*', '', user_input)
        
        # 查找"用户问题:"后的内容
        question_match = re.search(r'用户问题\s*[:：]\s*(.+)', question, re.IGNORECASE)
        if question_match:
            question = question_match.group(1).strip()
        
        return question.strip()
    
    def _extract_province_code(self, text: str) -> Optional[str]:
        """提取省份代码"""
        if not text:
            return None
            
        # 查找"数据库:省份代码"的模式
        pattern = r'数据库\s*[:：]\s*([A-Z]{1,3})'
        match = re.search(pattern, text)
        if match:
            return match.group(1).upper()
        
        return None
    
    def _extract_time_range(self, question: str) -> Optional[str]:
        """提取时间范围，智能补全年份"""
        import datetime
        current_year = datetime.datetime.now().year

        # 匹配各种时间表达
        time_patterns = [
            (r'(\d{4}年\d{1,2}月)', 'full'),      # 完整年月：2025年7月
            (r'(\d{4}-\d{1,2})', 'dash'),        # 短横线格式：2025-07
            (r'(\d{1,2}月)', 'month_only'),      # 仅月份：7月
            (r'(今年|去年|上月|本月)', 'relative'), # 相对时间
            (r'(最近\d+个月)', 'recent'),         # 最近几个月
        ]

        for pattern, pattern_type in time_patterns:
            match = re.search(pattern, question)
            if match:
                matched_text = match.group(1)

                if pattern_type == 'full':
                    # 已经是完整格式，直接返回
                    return matched_text
                elif pattern_type == 'month_only':
                    # 只有月份，智能补全年份
                    month_num = re.search(r'(\d{1,2})', matched_text).group(1)
                    # 🔧 智能年份补全：默认使用当前年份
                    return f"{current_year}年{month_num}月"
                elif pattern_type == 'relative':
                    # 相对时间转换
                    if matched_text == '今年':
                        return f"{current_year}年"
                    elif matched_text == '去年':
                        return f"{current_year-1}年"
                    elif matched_text in ['上月', '本月']:
                        current_month = datetime.datetime.now().month
                        if matched_text == '上月':
                            if current_month == 1:
                                return f"{current_year-1}年12月"
                            else:
                                return f"{current_year}年{current_month-1}月"
                        else:  # 本月
                            return f"{current_year}年{current_month}月"
                else:
                    # 其他格式，返回原始匹配
                    return matched_text

        return None
    
    def _classify_intent(self, question: str, vector_prompts: List = None) -> Tuple[IntentType, float]:
        """分类意图类型"""
        
        # 如果有向量匹配的复杂提示词，优先判断为复杂分析
        if vector_prompts and len(vector_prompts) > 0:
            logger.info(f"🎯 [意图分析] 检测到向量匹配的复杂提示词，判断为复杂分析")
            return IntentType.COMPLEX_ANALYSIS, 0.95
        
        # 检查复杂分析关键词
        complex_score = sum(1 for keyword in self.complex_keywords if keyword in question)
        simple_score = sum(1 for keyword in self.simple_keywords if keyword in question)
        
        if complex_score > simple_score:
            return IntentType.COMPLEX_ANALYSIS, min(0.8 + complex_score * 0.1, 0.95)
        elif simple_score > 0:
            return IntentType.SIMPLE_QUERY, min(0.7 + simple_score * 0.1, 0.9)
        else:
            # 默认为简单查询
            return IntentType.SIMPLE_QUERY, 0.6
    
    def _analyze_step_requirements(self, question: str, vector_prompts: List = None) -> Tuple[bool, List[str]]:
        """分析步骤要求"""
        
        # 如果有向量匹配的复杂提示词，解析其步骤要求
        if vector_prompts and len(vector_prompts) > 0:
            prompt_data, similarity = vector_prompts[0]
            if hasattr(prompt_data, 'content') and prompt_data.content:
                steps = self._parse_steps_from_prompt(prompt_data.content)
                if steps:
                    logger.info(f"📋 [意图分析] 从复杂提示词中解析到 {len(steps)} 个步骤")
                    return True, steps
        
        # 基于关键词判断
        multi_step_indicators = [
            "存在哪些", "风险", "分析", "明细", "详情", "对比"
        ]
        
        if any(indicator in question for indicator in multi_step_indicators):
            # 默认的多步骤流程
            default_steps = [
                "查询总体情况",
                "逐一分析各项指标", 
                "查询重点风险详情",
                "搜索改进建议"
            ]
            return True, default_steps
        
        return False, []
    
    def _parse_steps_from_prompt(self, prompt_content: str) -> List[str]:
        """从复杂提示词中解析步骤"""
        steps = []
        
        # 查找编号的步骤
        step_pattern = r'(\d+)\.\s*([^0-9\n]+?)(?=\d+\.|$)'
        matches = re.findall(step_pattern, prompt_content, re.DOTALL)
        
        for step_num, step_content in matches:
            # 清理步骤内容
            clean_step = re.sub(r'\s+', ' ', step_content.strip())
            if clean_step and len(clean_step) > 10:  # 过滤太短的内容
                steps.append(f"步骤{step_num}: {clean_step[:100]}...")  # 限制长度
        
        return steps
    
    def _map_province_to_database(self, province_code: str) -> Optional[str]:
        """将省份代码映射到数据库名"""
        if not province_code:
            return None
            
        # 这里应该从配置中获取映射关系
        # 暂时硬编码一些常见的映射
        mapping = {
            "GX": "analysis_gx",
            "GZ": "analysis_gz", 
            "JT": "analysis_jt"
        }
        
        return mapping.get(province_code.upper())

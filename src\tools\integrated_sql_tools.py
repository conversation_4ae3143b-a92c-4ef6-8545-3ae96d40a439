"""
集成SQL工具
将SQL生成和执行合并为单一工具，提供端到端的查询体验
"""

import json
import requests
import logging
import time
import re
from typing import Dict, Any, Optional
import pymysql
from pydantic import BaseModel, Field

from .base_tools import BaseTool, ToolInput, ToolOutput
from src.config.database_config import DatabaseConfig
from pydantic import Field
from typing import Dict, Any, List, Optional
import requests
import pymysql
from src.config import settings

# 设置日志记录器
logger = logging.getLogger(__name__)


class IntegratedSQLInput(ToolInput):
    """集成SQL工具输入"""
    question: str = Field(description="用户的自然语言问题")
    database_type: str = Field(default="mysql", description="数据库类型 (mysql, sqlite, postgresql)")
    system_message: Optional[str] = Field(default=None, description="系统消息，用于提取省份代码")


class IntegratedSQLTool(BaseTool):
    """集成SQL工具 - 从自然语言问题到SQL执行的完整流程"""
    
    def __init__(self, 
                 api_url: str = None,
                 api_key: str = None,
                 database_config: Optional[DatabaseConfig] = None):
        """
        初始化集成SQL工具
        
        Args:
            api_url: SQL生成API地址（可选，默认使用环境变量）
            api_key: API密钥（可选，默认使用环境变量）
            database_config: 数据库配置
        """
        super().__init__(
            name="integrated_sql",
            description="从数据库查询具体数据值，包括电费、用电量、各种业务指标（如铁塔共享率、用电负荷等）的具体数值。当用户需要查询实际数据、统计数据或指标数值时使用此工具。"
        )
        self.api_url = api_url or settings.sql_generator_api_url
        self.api_key = api_key or settings.sql_generator_api_key
        self.database_config = database_config or DatabaseConfig()

    def _extract_province_code_from_system(self, system_message: str) -> Optional[str]:
        """
        从系统消息中提取省份代码

        Args:
            system_message: 系统消息内容

        Returns:
            str: 省份代码，如 'GZ'，如果未找到返回None
        """
        if not system_message:
            return None

        # 查找"数据库:省份代码"的模式
        pattern = r'数据库\s*[:：]\s*([A-Z]{1,3})'
        match = re.search(pattern, system_message)
        if match:
            province_code = match.group(1).upper()
            logger.info(f"🔍 [省份提取] 从系统消息中提取到省份代码: {province_code}")
            return province_code

        # 备用模式：直接查找大写字母组合
        pattern2 = r'\b([A-Z]{2,3})\b'
        matches = re.findall(pattern2, system_message)
        if matches:
            # 检查是否是有效的省份代码
            province_mapping = settings.get_province_database_mapping()
            for match in matches:
                if match in province_mapping:
                    logger.info(f"🔍 [省份提取] 从系统消息中提取到有效省份代码: {match}")
                    return match

        logger.warning(f"⚠️ [省份提取] 未能从系统消息中提取省份代码: {system_message}")
        return None
    
    def call(self, question: str, database_type: str = "mysql", system_message: str = None,
             target_database: str = None) -> ToolOutput:
        """
        执行集成SQL查询

        Args:
            question: 用户的自然语言问题
            database_type: 数据库类型
            system_message: 系统消息，用于提取省份代码
            target_database: 目标数据库名称（可选，优先级高于system_message）

        Returns:
            包含SQL和执行结果的输出
        """
        total_start_time = time.time()
        timing_info = {}

        try:
            logger.info(f"🚀 [集成SQL] 开始处理问题: '{question}'")
            logger.info(f"🚀 [集成SQL] 数据库类型: {database_type}")
            if system_message:
                logger.info(f"🚀 [集成SQL] 系统消息: '{system_message}'")

            # 确定目标数据库
            final_target_database = None

            # 优先使用直接指定的target_database参数
            if target_database:
                # 统一通过省份代码映射获取数据库名
                mapped_db = settings.get_database_name_by_province(target_database)
                if mapped_db:
                    final_target_database = mapped_db
                    logger.info(f"🎯 [数据库选择] 省份代码映射: {target_database} → {final_target_database}")
                else:
                    # 如果映射失败，直接使用指定的数据库名
                    final_target_database = target_database
                    logger.info(f"🎯 [数据库选择] 直接使用数据库: {final_target_database}")
            else:
                # 从system_message提取省份代码
                province_code = None
                if system_message:
                    province_code = self._extract_province_code_from_system(system_message)
                    if province_code:
                        final_target_database = settings.get_database_name_by_province(province_code)
                        if final_target_database:
                            logger.info(f"🎯 [数据库选择] 系统消息省份代码 {province_code} 映射到数据库: {final_target_database}")
                        else:
                            logger.warning(f"⚠️ [数据库选择] 省份代码 {province_code} 未找到对应的数据库配置")

                if not final_target_database:
                    # 使用默认数据库
                    final_target_database = settings.mysql_database
                    logger.info(f"🎯 [数据库选择] 使用默认数据库: {final_target_database}")

            # 更新变量名以保持后续代码兼容
            target_database = final_target_database

            # 第一步：生成SQL
            step1_start = time.time()
            step_msg = "🔄 步骤1: 生成SQL查询..."
            print(step_msg)
            logger.info(f"📊 [SQL工具] {step_msg}")
            
            sql_result = self._generate_sql(question)
            step1_duration = time.time() - step1_start
            timing_info['sql_generation'] = step1_duration
            
            if not sql_result["success"]:
                error_msg = f"SQL生成失败: {sql_result['error']} (用时: {step1_duration:.2f}秒)"
                logger.error(f"❌ [SQL工具] {error_msg}")
                return ToolOutput(
                    success=False,
                    result=None,
                    error=error_msg
                )
            
            generated_sql = sql_result["sql"]
            success_msg = f"✅ 生成的SQL: {generated_sql} (用时: {step1_duration:.2f}秒)"
            print(success_msg)
            logger.info(f"📊 [SQL工具] {success_msg}")
            
            # 第二步：执行SQL
            step2_start = time.time()
            exec_msg = "🔄 步骤2: 执行SQL查询..."
            print(exec_msg)
            logger.info(f"📊 [SQL工具] {exec_msg}")
            
            execution_result = self._execute_sql(generated_sql, database_type, target_database)
            step2_duration = time.time() - step2_start
            timing_info['sql_execution'] = step2_duration
            
            if not execution_result["success"]:
                error_msg = f"SQL执行失败: {execution_result['error']} (用时: {step2_duration:.2f}秒)"
                logger.error(f"❌ [SQL工具] {error_msg}")
                return ToolOutput(
                    success=False,
                    result=None,
                    error=error_msg
                )
            
            # 组合结果
            total_duration = time.time() - total_start_time
            timing_info['total'] = total_duration
            
            final_result = {
                "question": question,
                "generated_sql": generated_sql,
                "execution_result": execution_result["result"],
                "summary": self._generate_summary(question, execution_result["result"]),
                "timing": timing_info
            }
            
            row_count = len(execution_result['result'].get('data', []))
            complete_msg = f"✅ 查询完成，返回 {row_count} 条记录 (总用时: {total_duration:.2f}秒，SQL生成: {step1_duration:.2f}秒，执行: {step2_duration:.2f}秒)"
            print(complete_msg)
            logger.info(f"📊 [SQL工具] {complete_msg}")
            
            # 记录更详细的执行结果信息
            if row_count > 0:
                columns = execution_result['result'].get('columns', [])
                logger.info(f"📊 [SQL结果详情] 列数: {len(columns)}, 列名: {columns}")
                
                # 记录前几行数据的概况（不记录具体数据以避免日志过长）
                sample_data = execution_result['result'].get('data', [])[:3]
                logger.info(f"📊 [SQL数据样例] 前3行数据: {sample_data}")
            else:
                logger.info(f"📊 [SQL结果详情] 查询未返回任何数据")
            
            return ToolOutput(
                success=True,
                result=final_result,
                error=None
            )
            
        except Exception as e:
            total_duration = time.time() - total_start_time
            error_msg = f"集成SQL查询失败: {str(e)} (总用时: {total_duration:.2f}秒)"
            logger.error(f"💥 [SQL工具异常] {error_msg}")
            return ToolOutput(
                success=False,
                result=None,
                error=error_msg
            )
    
    def get_schema(self) -> Dict[str, Any]:
        """获取工具模式"""
        return {
            "type": "function",
            "function": {
                "name": self.name,
                "description": self.description,
                "parameters": {
                    "type": "object",
                    "properties": {
                        "question": {
                            "type": "string",
                            "description": "用户的自然语言问题，例如：'2025年4月电费是多少'"
                        },
                        "database_type": {
                            "type": "string",
                            "description": "数据库类型，默认为mysql",
                            "default": "mysql",
                            "enum": ["mysql", "postgresql"]
                        },
                        "system_message": {
                            "type": "string",
                            "description": "系统消息，用于提取省份代码以选择对应的数据库，例如：'数据库:GZ'",
                            "default": None
                        }
                    },
                    "required": ["question"]
                }
            }
        }
    
    def _generate_sql(self, question: str) -> Dict[str, Any]:
        """生成SQL查询"""
        start_time = time.time()
        try:
            logger.info(f"🔧 [SQL生成] 开始生成SQL，问题: '{question}'")
            logger.info(f"🔧 [SQL生成] API地址: {self.api_url}")
            
            headers = {
                'Accept': 'application/json, text/plain, */*',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive',
                'Content-Type': 'application/json',
                'X-API-Key': self.api_key,
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
            
            # 发送请求
            request_start = time.time()
            logger.info(f"🌐 [SQL生成] 发送API请求...")
            response = requests.get(
                self.api_url,
                params={'question': question},
                headers=headers,
                timeout=30
            )
            request_duration = time.time() - request_start
            
            logger.info(f"🌐 [SQL生成] API响应状态码: {response.status_code} (请求用时: {request_duration:.2f}秒)")
            
            if response.status_code == 200:
                result = response.json()
                logger.info(f"📥 [SQL生成] API返回数据: {result}")
                
                # 处理不同的API响应格式
                sql_query = None
                
                # 新格式：{"id": "...", "text": "SQL...", "type": "sql"}
                if 'text' in result and result.get('type') == 'sql':
                    sql_query = result['text']
                    logger.info(f"✅ [SQL生成] 使用新格式解析，type=sql")
                # 旧格式：{"sql": "SELECT ..."}
                elif 'sql' in result:
                    sql_query = result['sql']
                    logger.info(f"✅ [SQL生成] 使用旧格式解析，找到sql字段")
                # 备用格式：{"query": "SELECT ..."}
                elif 'query' in result:
                    sql_query = result['query']
                    logger.info(f"✅ [SQL生成] 使用备用格式解析，找到query字段")
                
                total_duration = time.time() - start_time
                if sql_query:
                    logger.info(f"✅ [SQL生成] 成功生成SQL: {sql_query.strip()} (总用时: {total_duration:.2f}秒)")
                    return {"success": True, "sql": sql_query.strip()}
                else:
                    error_msg = f"API返回格式错误，未找到SQL语句: {result} (总用时: {total_duration:.2f}秒)"
                    logger.error(f"❌ [SQL生成] {error_msg}")
                    return {"success": False, "error": error_msg}
            else:
                total_duration = time.time() - start_time
                error_msg = f"API请求失败，状态码: {response.status_code}，响应: {response.text} (总用时: {total_duration:.2f}秒)"
                logger.error(f"❌ [SQL生成] {error_msg}")
                return {"success": False, "error": error_msg}
                
        except requests.exceptions.RequestException as e:
            total_duration = time.time() - start_time
            error_msg = f"网络请求失败: {str(e)} (总用时: {total_duration:.2f}秒)"
            logger.error(f"🌐 [SQL生成] {error_msg}")
            return {"success": False, "error": error_msg}
        except Exception as e:
            total_duration = time.time() - start_time
            error_msg = f"SQL生成失败: {str(e)} (总用时: {total_duration:.2f}秒)"
            logger.error(f"💥 [SQL生成] {error_msg}")
            return {"success": False, "error": error_msg}
    
    def _execute_sql(self, sql: str, database_type: str, target_database: str = None) -> Dict[str, Any]:
        """执行SQL查询"""
        start_time = time.time()
        try:
            logger.info(f"🔧 [SQL执行] 开始执行SQL查询")
            logger.info(f"🔧 [SQL执行] 数据库类型: {database_type}")
            logger.info(f"🔧 [SQL执行] 原始SQL语句: {sql}")
            
            # 安全检查
            safety_start = time.time()
            if not self._is_safe_query(sql):
                safety_duration = time.time() - safety_start
                total_duration = time.time() - start_time
                error_msg = f"出于安全考虑，只允许执行SELECT查询 (安全检查用时: {safety_duration:.3f}秒，总用时: {total_duration:.2f}秒)"
                logger.error(f"🚫 [SQL执行] {error_msg}")
                return {
                    "success": False,
                    "error": error_msg
                }
            
            safety_duration = time.time() - safety_start
            logger.info(f"✅ [SQL执行] SQL安全检查通过 (用时: {safety_duration:.3f}秒)")
            
            # 清理SQL：移除注释行，避免MySQL执行问题
            cleaned_sql = self._clean_sql_for_execution(sql)
            logger.info(f"🧹 [SQL执行] 清理后的SQL: {cleaned_sql}")
            
            if database_type.lower() == "mysql":
                return self._execute_mysql(cleaned_sql, start_time, target_database)
            else:
                total_duration = time.time() - start_time
                error_msg = f"不支持的数据库类型: {database_type} (总用时: {total_duration:.2f}秒)"
                logger.error(f"❌ [SQL执行] {error_msg}")
                return {
                    "success": False,
                    "error": error_msg
                }
                
        except Exception as e:
            total_duration = time.time() - start_time
            error_msg = f"SQL执行失败: {str(e)} (总用时: {total_duration:.2f}秒)"
            logger.error(f"💥 [SQL执行] {error_msg}")
            return {
                "success": False,
                "error": error_msg
            }
    
    def _is_safe_query(self, sql: str) -> bool:
        """检查SQL查询是否安全"""
        import re
        
        sql_upper = sql.upper().strip()
        
        # 先移除字符串字面量，避免误判字符串内容
        # 移除单引号字符串
        sql_cleaned = re.sub(r"'[^']*'", "", sql_upper)
        # 移除双引号字符串  
        sql_cleaned = re.sub(r'"[^"]*"', "", sql_cleaned)
        # 移除反引号字符串（MySQL字段名）
        sql_cleaned = re.sub(r'`[^`]*`', "", sql_cleaned)
        
        # 移除合法的SQL注释行（以--开头的整行注释）
        sql_lines = sql_cleaned.split('\n')
        non_comment_lines = []
        for line in sql_lines:
            line_stripped = line.strip()
            # 如果不是以--开头的注释行，保留这一行
            if not line_stripped.startswith('--'):
                non_comment_lines.append(line)
        sql_cleaned = '\n'.join(non_comment_lines)
        
        # 检查是否还有SELECT语句（移除注释后）
        sql_cleaned_stripped = sql_cleaned.strip()
        if not sql_cleaned_stripped.upper().startswith('SELECT'):
            return False
        
        # 使用更精确的正则表达式检查危险操作
        # 这些模式检查SQL关键词是否作为独立的词出现，而不是字段名的一部分
        dangerous_patterns = [
            r'\bDROP\b',           # DROP语句
            r'\bDELETE\b\s+FROM',  # DELETE FROM语句（不匹配字段名中的delete）
            r'\bUPDATE\b\s+\w+\s+SET',  # UPDATE SET语句
            r'\bINSERT\b\s+INTO',  # INSERT INTO语句
            r'\bALTER\b\s+TABLE',  # ALTER TABLE语句
            r'\bCREATE\b\s+(TABLE|INDEX|VIEW)', # CREATE语句
            r'\bTRUNCATE\b',       # TRUNCATE语句
            r'\bEXEC(UTE)?\b',     # EXEC/EXECUTE语句
            r';--',                # 危险的注释注入（分号+注释）
            r'/\*.*?\*/',          # 多行注释
            r'\bUNION\b.*\bSELECT\b', # UNION SELECT注入
            r'\bINTO\s+OUTFILE\b', # 文件导出
            r'\bLOAD_FILE\b',      # 文件读取
        ]
        
        # 在清理后的SQL上检查危险模式
        for pattern in dangerous_patterns:
            if re.search(pattern, sql_cleaned):
                return False
        
        # 额外检查：确保没有多条语句（用分号分隔）
        # 在已清理字符串的SQL上检查分号
        if ';' in sql_cleaned.rstrip(';'):  # 允许末尾的分号
            return False
        
        return True
    
    def _clean_sql_for_execution(self, sql: str) -> str:
        """清理SQL，移除注释行但保留所有有效的SQL内容"""
        import re
        
        # 按行处理，移除注释行
        sql_lines = sql.split('\n')
        cleaned_lines = []
        
        for line in sql_lines:
            line_stripped = line.strip()
            # 跳过空行和以--开头的注释行
            if line_stripped and not line_stripped.startswith('--'):
                # 移除行内注释（-- 后面的内容）
                if '--' in line:
                    # 但要保证--不在字符串内
                    in_string = False
                    quote_char = None
                    cleaned_line = ""
                    i = 0
                    while i < len(line):
                        char = line[i]
                        
                        # 处理字符串开始/结束
                        if char in ["'", '"'] and not in_string:
                            in_string = True
                            quote_char = char
                            cleaned_line += char
                        elif char == quote_char and in_string:
                            in_string = False
                            quote_char = None
                            cleaned_line += char
                        # 处理注释
                        elif char == '-' and i + 1 < len(line) and line[i + 1] == '-' and not in_string:
                            # 找到注释，截断这一行
                            break
                        else:
                            cleaned_line += char
                        i += 1
                    
                    cleaned_lines.append(cleaned_line.rstrip())
                else:
                    cleaned_lines.append(line)
        
        # 重新组合SQL
        cleaned_sql = '\n'.join(cleaned_lines)
        
        # 移除多行注释 /* ... */（但要小心不在字符串内）
        cleaned_sql = re.sub(r'/\*.*?\*/', '', cleaned_sql, flags=re.DOTALL)
        
        # 移除多余的空行
        cleaned_sql = re.sub(r'\n\s*\n', '\n', cleaned_sql).strip()
        
        return cleaned_sql
    
    def _execute_mysql(self, sql: str, start_time: float, target_database: str = None) -> Dict[str, Any]:
        """执行MySQL查询"""
        connection = None
        try:
            # 获取MySQL配置
            config_start = time.time()
            config = self.database_config.get_mysql_config(target_database)
            config_duration = time.time() - config_start
            logger.info(f"🔗 [MySQL] 连接数据库: {config['host']}:{config['port']}/{config['database']}")
            logger.info(f"⚙️ [MySQL] 配置加载用时: {config_duration:.3f}秒")
            
            # 建立连接
            connect_start = time.time()
            connection = pymysql.connect(
                host=config["host"],
                port=config["port"],
                user=config["user"],
                password=config["password"],
                database=config["database"],
                charset=config.get("charset", "utf8mb4"),
                cursorclass=pymysql.cursors.DictCursor
            )
            connect_duration = time.time() - connect_start
            
            logger.info(f"✅ [MySQL] 数据库连接成功 (连接用时: {connect_duration:.3f}秒)")
            
            try:
                with connection.cursor() as cursor:
                    query_start = time.time()
                    logger.info(f"🔍 [MySQL] 开始执行查询...")
                    cursor.execute(sql)
                    result = cursor.fetchall()
                    query_duration = time.time() - query_start
                    
                    # 获取列信息
                    columns = [desc[0] for desc in cursor.description] if cursor.description else []
                    
                    total_duration = time.time() - start_time
                    logger.info(f"✅ [MySQL] 查询执行成功 (查询用时: {query_duration:.3f}秒，总用时: {total_duration:.2f}秒)")
                    logger.info(f"📊 [MySQL] 结果统计: {len(result)} 行, {len(columns)} 列")
                    if columns:
                        logger.info(f"📊 [MySQL] 列名: {columns}")
                    
                    return {
                        "success": True,
                        "result": {
                            "columns": columns,
                            "data": result,
                            "row_count": len(result),
                            "sql": sql,
                            "timing": {
                                "config_loading": config_duration,
                                "connection": connect_duration,
                                "query_execution": query_duration,
                                "total": total_duration
                            }
                        }
                    }
            finally:
                close_start = time.time()
                connection.close()
                close_duration = time.time() - close_start
                logger.info(f"🔌 [MySQL] 数据库连接已关闭 (关闭用时: {close_duration:.3f}秒)")
                
        except Exception as e:
            total_duration = time.time() - start_time
            error_msg = f"MySQL执行失败: {str(e)} (总用时: {total_duration:.2f}秒)"
            logger.error(f"💥 [MySQL] {error_msg}")
            return {
                "success": False,
                "error": error_msg
            }
    
    def _generate_summary(self, question: str, result: Dict[str, Any]) -> str:
        """生成查询结果摘要"""
        data = result.get("data", [])
        row_count = result.get("row_count", 0)
        columns = result.get("columns", [])
        
        if row_count == 0:
            return f"根据问题「{question}」，查询未返回任何结果。"
        
        # 如果有列信息，生成表格格式
        if columns and data:
            table_content = self._format_as_table(columns, data, row_count)
            summary = table_content
        else:
            # 备用格式：列表形式
            summary = f"**{question}的查询结果如下：**\n\n"
            list_content = self._format_as_list(data, row_count)
            summary += list_content
        
        # 添加数据概览 - 确保与表格分离
        summary += "\n\n**数据概览**\n"
        summary += f"- 总记录数：{row_count}条\n"
        
        # 如果有数值列，可以添加汇总信息
        if data and row_count > 0:
            # 寻找可能的数值列进行汇总
            for col in columns:
                if any(keyword in col.lower() for keyword in ['费', 'amount', 'value', '金额', '数量']):
                    try:
                        total_value = sum(float(row.get(col, 0) or 0) for row in data)
                        if total_value > 0:
                            # 使用与表格一致的格式化规则
                            if self._is_percentage_field(col, total_value):
                                # 百分比保留4位小数
                                formatted_value = f"{total_value * 100:.4f}%" if total_value <= 1 else f"{total_value:.4f}%"
                            else:
                                # 普通数值保留2位小数
                                formatted_value = f"{total_value:.2f}"
                            summary += f"- {col}总计：{formatted_value}\n"
                    except:
                        continue
        
        # 添加友好的结束语
        summary += "\n*数据查询完成，如需更多分析请继续提问。*"
        
        return summary
    
    def _format_as_table(self, columns: list, data: list, row_count: int) -> str:
        """将查询结果格式化为标准markdown表格"""
        if not data:
            return "查询结果为空。"

        # 限制显示的行数
        display_limit = 10
        display_data = data[:display_limit]

        # 🔧 修复：确保列数一致性，清理列名
        cleaned_columns = []
        for col in columns:
            # 清理列名，移除可能的特殊字符
            clean_col = str(col).strip().replace('|', '').replace('\n', '').replace('\r', '')
            if not clean_col:
                clean_col = "未命名列"
            cleaned_columns.append(clean_col)

        # 数据格式化函数
        def format_value(value, column_name=""):
            if value is None:
                return "null"

            # 清理值中的特殊字符，防止破坏表格格式
            str_value = str(value).replace('|', '｜').replace('\n', ' ').replace('\r', ' ').strip()

            # 日期格式化
            if isinstance(value, str) and self._is_date_like(value):
                return self._format_date(value)

            # 数值格式化
            if isinstance(value, (int, float)):
                # 检查是否为百分比字段（根据列名或数值范围判断）
                if self._is_percentage_field(column_name, value):
                    # 百分比保留4位小数
                    return f"{value * 100:.4f}%" if value <= 1 else f"{value:.4f}%"
                # 普通数值保留两位小数
                if isinstance(value, float):
                    return f"{value:.2f}"
                return str(value)

            return str_value

        # 🔧 修复：严格确保表头和分隔符列数一致
        num_columns = len(cleaned_columns)
        header_parts = cleaned_columns[:]  # 复制列表
        separator_parts = ["---"] * num_columns  # 确保分隔符数量与列数一致

        # 构建标准markdown表格
        header_line = "| " + " | ".join(header_parts) + " |"
        separator_line = "| " + " | ".join(separator_parts) + " |"

        # 构建表格
        table_lines = []
        # 添加表格开始标记
        table_lines.append("**查询结果：**")
        table_lines.append("")  # 空行
        table_lines.append(header_line)
        table_lines.append(separator_line)

        # 生成数据行 - 严格确保每行列数一致
        for row in display_data:
            row_parts = []
            for col in cleaned_columns:
                # 获取原始列名对应的值
                original_col = columns[cleaned_columns.index(col)]
                value = row.get(original_col, '')
                formatted_value = format_value(value, original_col)

                # 如果值太长，截断并添加省略号
                if len(str(formatted_value)) > 30:
                    formatted_value = str(formatted_value)[:27] + "..."

                row_parts.append(str(formatted_value))

            # 🔧 修复：确保每行的列数与表头一致
            while len(row_parts) < num_columns:
                row_parts.append("")  # 补齐缺失的列
            row_parts = row_parts[:num_columns]  # 截断多余的列

            row_line = "| " + " | ".join(row_parts) + " |"
            table_lines.append(row_line)

        # 表格结束
        table_lines.append("")  # 空行

        # 如果有更多数据，添加提示
        if row_count > display_limit:
            table_lines.append(f"*注：共 {row_count} 条记录，仅显示前 {display_limit} 条*")
            table_lines.append("")  # 空行

        # 🔧 修复：使用正常的换行符而不是转义的换行符
        result = "\n".join(table_lines)

        # 调试输出 - 验证表格格式
        data_rows = len(display_data)
        text_lines = len(table_lines)
        print(f"🔍 DEBUG: 表格生成完成，列数: {num_columns}，数据行数: {data_rows}，表格文本行数: {text_lines}")
        print(f"🔍 DEBUG: 表头预览: {header_line}")
        print(f"🔍 DEBUG: 分隔符预览: {separator_line}")

        # 🔧 验证表格格式正确性
        table_content_lines = [line for line in table_lines if line.startswith('|')]
        if table_content_lines:
            header_cols = table_content_lines[0].count('|') - 1
            separator_cols = table_content_lines[1].count('|') - 1 if len(table_content_lines) > 1 else 0
            print(f"🔍 DEBUG: 表头列数: {header_cols}，分隔符列数: {separator_cols}")

            # 检查数据行列数
            for i, line in enumerate(table_content_lines[2:], 1):
                data_cols = line.count('|') - 1
                if data_cols != header_cols:
                    print(f"⚠️ WARNING: 第{i}行列数不匹配，期望{header_cols}列，实际{data_cols}列")

        return result
    
    def _is_date_like(self, value: str) -> bool:
        """判断字符串是否为日期格式"""
        import re
        date_patterns = [
            r'\d{4}-\d{1,2}-\d{1,2}',  # YYYY-MM-DD
            r'\d{4}/\d{1,2}/\d{1,2}',  # YYYY/MM/DD
            r'\d{1,2}-\d{1,2}-\d{4}',  # DD-MM-YYYY
        ]
        return any(re.match(pattern, str(value)) for pattern in date_patterns)
    
    def _format_date(self, date_str: str) -> str:
        """格式化日期为 YYYY-MM-DD 格式"""
        import re
        from datetime import datetime
        
        try:
            # 尝试不同的日期格式
            date_formats = [
                "%Y-%m-%d",
                "%Y/%m/%d", 
                "%d-%m-%Y",
                "%d/%m/%Y",
                "%Y-%m-%d %H:%M:%S"
            ]
            
            for fmt in date_formats:
                try:
                    dt = datetime.strptime(str(date_str), fmt)
                    return dt.strftime("%Y-%m-%d")
                except ValueError:
                    continue
            
            return str(date_str)  # 如果无法解析，返回原始值
        except:
            return str(date_str)
    
    def _is_percentage_field(self, column_name: str, value) -> bool:
        """判断是否为百分比字段"""
        if not isinstance(value, (int, float)):
            return False
        
        # 🔧 优化：更精确地根据列名判断百分比字段
        percentage_keywords = [
            'ratio', 'rate', 'percent', 'pct', 'percentage',
            '比例', '占比', '百分比', '率', '比率',
            'share', 'portion', 'fraction'
        ]
        col_lower = column_name.lower()
        
        # 必须列名明确包含百分比关键词才判断为百分比
        has_percentage_keyword = any(keyword in col_lower for keyword in percentage_keywords)
        
        if has_percentage_keyword:
            return True
        
        # 🔧 移除基于数值范围的判断，避免误判价格等小数
        # 之前的逻辑：0-1之间的小数可能是百分比，但这会误判价格、系数等
        # 现在只基于列名判断，更可靠
        
        return False
    
    def _format_as_list(self, data: list, row_count: int) -> str:
        """将查询结果格式化为列表（备用格式）"""
        # 添加数据摘要
        if row_count == 1:
            summary = "查询结果:\n"
            for key, value in data[0].items():
                summary += f"  {key}: {value}\n"
        elif row_count <= 5:
            summary = "查询结果:\n"
            for i, row in enumerate(data, 1):
                summary += f"  记录{i}: {dict(row)}\n"
        else:
            summary = f"前3条记录：\n"
            for i, row in enumerate(data[:3], 1):
                summary += f"  记录{i}: {dict(row)}\n"
            summary += f"  ... 还有 {row_count - 3} 条记录"
        
        return summary
    
    def get_input_schema(self) -> Dict[str, Any]:
        """获取输入参数schema"""
        return IntegratedSQLInput.model_json_schema()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "name": self.name,
            "description": self.description,
            "input_schema": self.get_input_schema()
        } 